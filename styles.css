/* CSS Custom Properties for Dynamic Viewport */
:root {
    --vh: 1vh;
    --vw: 1vw;
    /* Fallback for browsers that don't support dvh */
    --app-height: 100vh;
    /* Safe area insets for devices with notches */
    --safe-area-inset-top: env(safe-area-inset-top, 0px);
    --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
    --safe-area-inset-left: env(safe-area-inset-left, 0px);
    --safe-area-inset-right: env(safe-area-inset-right, 0px);
}

/* Support for dynamic viewport units */
@supports (height: 100dvh) {
    :root {
        --app-height: 100dvh;
    }
}

/* Specific support for Samsung Browser and other mobile browsers */
@supports (-webkit-touch-callout: none) {
    :root {
        --app-height: -webkit-fill-available;
    }
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #212121;
    color: #ffffff;
    /* Use CSS custom property for dynamic height */
    height: var(--app-height);
    min-height: var(--app-height);
    overflow: hidden;
    /* Prevent horizontal scrolling on mobile */
    overflow-x: hidden;
    /* Improve touch scrolling on iOS */
    -webkit-overflow-scrolling: touch;
    /* Prevent text selection on mobile for better UX */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    /* Ensure proper rendering on mobile */
    position: relative;
}

.app-container {
    display: flex;
    height: var(--app-height);
    min-height: var(--app-height);
    /* Ensure container adapts to mobile browser UI changes */
    position: relative;
    width: 100%;
    max-width: 100vw;
}

/* Sidebar backdrop - hidden by default on desktop */
.sidebar-backdrop {
    display: none;
}

/* Sidebar Styles */
.sidebar {
    width: 320px;
    background-color: #171717;
    border-right: 1px solid #2d2d2d;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 100;
    overflow: visible;
}

.sidebar.hidden {
    transform: translateX(-100%);
}

.sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #2d2d2d;
}

.new-chat-btn {
    width: 100%;
    padding: 12px 16px;
    background-color: transparent;
    border: 1px solid #4d4d4d;
    border-radius: 8px;
    color: #ffffff;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: background-color 0.2s;
}

.new-chat-btn:hover {
    background-color: #2d2d2d;
}

.chat-history {
    flex: 1;
    padding: 8px;
    overflow-y: auto;
    overflow-x: visible;
}

.chat-item {
    padding: 14px 18px;
    margin: 4px 0;
    border-radius: 8px;
    cursor: pointer;
    font-size: 15px;
    color: #b3b3b3;
    transition: background-color 0.2s;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.chat-item:hover {
    background-color: #2d2d2d;
}

.chat-item.active {
    background-color: #2d2d2d;
    color: #ffffff;
}

.chat-title {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.chat-menu-button {
    background: none;
    border: none;
    color: #8d8d8d;
    font-size: 14px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    opacity: 0;
    transition: all 0.2s;
    margin-left: 8px;
    flex-shrink: 0;
}

.chat-item:hover .chat-menu-button {
    opacity: 1;
}

.chat-menu-button:hover {
    background-color: #3d3d3d;
    color: #ffffff;
}

.chat-dropdown-menu {
    position: fixed;
    background-color: #2d2d2d;
    border: 1px solid #4d4d4d;
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    z-index: 9999;
    min-width: 140px;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-8px);
    transition: all 0.2s;
}

.chat-dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

.dropdown-item {
    width: 100%;
    background: none;
    border: none;
    color: #b3b3b3;
    font-size: 14px;
    cursor: pointer;
    padding: 12px 16px;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s;
}

.dropdown-item:hover {
    background-color: #3d3d3d;
    color: #ffffff;
}

.dropdown-item:first-child {
    border-radius: 8px 8px 0 0;
}

.dropdown-item:last-child {
    border-radius: 0 0 8px 8px;
}

.dropdown-item:only-child {
    border-radius: 8px;
}

.delete-chat-btn {
    color: #ff6b6b !important;
}

.delete-chat-btn:hover {
    background-color: #ff6b6b !important;
    color: #ffffff !important;
}

.sidebar-footer {
    padding: 16px;
    border-top: 1px solid #2d2d2d;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #b3b3b3;
    font-size: 14px;
    margin-bottom: 12px;
    position: relative;
}

.logout-btn {
    background: none;
    border: none;
    color: #8d8d8d;
    font-size: 14px;
    cursor: pointer;
    padding: 4px 6px;
    border-radius: 4px;
    transition: all 0.2s;
    margin-left: auto;
}

.logout-btn:hover {
    background-color: #2d2d2d;
    color: #ff6b6b;
}

.profile-settings-btn {
    background: none;
    border: none;
    color: #b3b3b3;
    font-size: 14px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.profile-settings-btn:hover {
    background-color: #2d2d2d;
    color: #ffffff;
}

.profile-settings-btn i {
    font-size: 16px;
}

/* Settings Menu */
.settings-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: none;
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.settings-menu.show {
    display: flex;
}

.settings-content {
    background-color: #1a1a1a;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid #2d2d2d;
}

.settings-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid #2d2d2d;
}

.settings-header h3 {
    margin: 0;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
}

.settings-close-btn {
    background: none;
    border: none;
    color: #8d8d8d;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s;
}

.settings-close-btn:hover {
    background-color: #2d2d2d;
    color: #ffffff;
}

.settings-section {
    padding: 24px;
}

.settings-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;
    border-bottom: 1px solid #2d2d2d;
}

.settings-item:last-child {
    border-bottom: none;
}

.settings-item label {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
}

.settings-value {
    color: #b3b3b3;
    font-size: 14px;
}



.settings-toggle {
    position: relative;
    width: 50px;
    height: 24px;
}

.settings-toggle input[type="checkbox"] {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #404040;
    transition: 0.3s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: #ffffff;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #00FFFF;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.settings-actions {
    padding: 20px 24px;
    border-top: 1px solid #2d2d2d;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.settings-btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
}

.settings-btn.primary {
    background-color: #00FFFF;
    color: #000000;
}

.settings-btn.primary:hover {
    background-color: #00e6e6;
}

.settings-btn.secondary {
    background-color: transparent;
    color: #b3b3b3;
    border: 1px solid #404040;
}

.settings-btn.secondary:hover {
    background-color: #2d2d2d;
    color: #ffffff;
}

.settings-btn.danger {
    background-color: #dc3545;
    color: #ffffff;
    display: flex;
    align-items: center;
    gap: 8px;
}

.settings-btn.danger:hover {
    background-color: #c82333;
}

.danger-section {
    border-top: 1px solid #2d2d2d;
    margin-top: 16px;
    padding-top: 24px;
}

.danger-setting {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.danger-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}



@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.danger-description {
    color: #8d8d8d;
    font-size: 12px;
}

/* Success Message */
.success-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #4CAF50;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 1001;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation: slideInRight 0.3s ease-out;
}

.success-message i {
    font-size: 16px;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Enhanced Document Preview Styles */
.document-preview-enhanced {
    background: #3a3a3a;
    border: 2px solid #ffffff;
    border-radius: 12px;
    margin: 8px auto;
    color: #ffffff;
    font-size: 14px;
    max-width: 800px;
    width: 100%;
    box-sizing: border-box;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

/* Single Document Display */
.single-document-display {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    gap: 12px;
}

.single-document-display i {
    color: #ffffff;
    font-size: 16px;
    opacity: 0.9;
}

.single-document-display .document-text {
    flex: 1;
    font-weight: 500;
    font-size: 14px;
}

.single-document-display .document-text strong {
    font-weight: 600;
    color: #ffffff;
}

/* Multiple Documents Display */
.multiple-documents-display {
    position: relative;
}

.documents-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.documents-header:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.header-content i {
    color: #ffffff;
    font-size: 16px;
    opacity: 0.9;
}

.header-content .document-text {
    font-weight: 500;
    font-size: 14px;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.expand-arrow {
    color: #ffffff;
    font-size: 14px;
    transition: transform 0.3s ease;
    opacity: 0.8;
}

.multiple-documents-display.expanded .expand-arrow {
    transform: rotate(180deg);
}

.documents-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.multiple-documents-display.expanded .documents-content {
    max-height: 400px;
    padding: 16px 20px;
}

/* Clear Documents Button */
.clear-documents-btn {
    background: none;
    border: none;
    color: #ffffff;
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    opacity: 0.7;
    transition: all 0.2s ease;
    font-size: 12px;
}

.clear-documents-btn:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.1);
}

/* Enhanced Document Items */
.document-item-enhanced {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 13px;
    transition: background-color 0.2s ease;
}

.document-item-enhanced:last-child {
    border-bottom: none;
}

.document-item-enhanced:hover {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    margin: 0 -8px;
    padding: 10px 8px;
}

.document-item-enhanced i {
    color: #ffffff;
    width: 16px;
    opacity: 0.8;
}

.document-item-enhanced .doc-name {
    font-weight: 500;
    color: #ffffff;
    flex: 1;
}

.document-item-enhanced .doc-type {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.document-item-enhanced .doc-stats {
    color: rgba(255, 255, 255, 0.7);
    font-size: 11px;
}

/* Enhanced Document Stats */
.document-stats-enhanced {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.document-stats-enhanced i {
    margin-right: 6px;
    opacity: 0.8;
}

/* Document Preview Container */
.document-preview-container {
    padding: 0 24px;
    margin-bottom: 12px;
}

/* Responsive Design for Enhanced Document Preview */
@media (max-width: 768px) {
    .document-preview-enhanced {
        margin: 8px 16px;
        font-size: 13px;
    }

    .single-document-display {
        padding: 14px 16px;
        gap: 10px;
    }

    .documents-header {
        padding: 14px 16px;
    }

    .multiple-documents-display.expanded .documents-content {
        padding: 14px 16px;
    }

    .document-item-enhanced {
        gap: 10px;
        font-size: 12px;
    }

    .document-preview-container {
        padding: 0 16px;
    }
}

/* Animation for smooth expansion */
@keyframes expandContent {
    from {
        max-height: 0;
        opacity: 0;
    }
    to {
        max-height: 400px;
        opacity: 1;
    }
}

.multiple-documents-display.expanded .documents-content {
    animation: expandContent 0.3s ease-out;
}

/* Auth Error Styles */
.auth-error {
    background-color: #ff6b6b;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    font-size: 14px;
    text-align: center;
}

/* Code Copy Button Success State */
.code-copy-btn.success {
    background: linear-gradient(135deg, #4CAF50, #45a049) !important;
}

/* Code Block Positioning */
pre {
    position: relative;
}



.powered-by {
    text-align: center;
    color: #8d8d8d;
}

.powered-by small {
    display: block;
    font-size: 11px;
    margin-bottom: 4px;
}

.ai-branding {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 12px;
    color: #00FFFF;
}

.ai-branding i {
    font-size: 14px;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #212121;
    transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.main-content.sidebar-hidden {
    margin-left: -320px;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
}

.header {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    background-color: #212121;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 18px;
    cursor: pointer;
    margin-right: 16px;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.sidebar-toggle:hover {
    background-color: #2d2d2d;
    transform: scale(1.05);
}

.sidebar-toggle i {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-toggle:active i {
    transform: scale(0.95);
}

.app-title {
    flex: 1;
    font-size: 18px;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.new-chat-btn-header {
    background: none;
    border: none;
    color: #b3b3b3;
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s;
}

.new-chat-btn-header:hover {
    background-color: #2d2d2d;
    color: #ffffff;
}

/* Multi-Agent Toggle Button */
.multi-agent-toggle {
    background: linear-gradient(135deg, rgba(30, 30, 30, 0.8), rgba(40, 40, 40, 0.8));
    border: 2px solid rgba(0, 255, 255, 0.4);
    color: #e0e0e0;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    padding: 10px 16px;
    border-radius: 25px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.multi-agent-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.multi-agent-toggle:hover {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.15), rgba(255, 0, 255, 0.1));
    border-color: rgba(0, 255, 255, 0.7);
    color: #00FFFF;
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 255, 255, 0.2);
}

.multi-agent-toggle:hover::before {
    left: 100%;
}

.multi-agent-toggle.active {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.25), rgba(255, 0, 255, 0.2));
    border-color: #00FFFF;
    color: #ffffff;
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.4), 0 0 40px rgba(0, 255, 255, 0.2);
    animation: pulse-glow 2s infinite ease-in-out;
}

.multi-agent-toggle.active:hover {
    box-shadow: 0 0 25px rgba(0, 255, 255, 0.5), 0 0 50px rgba(0, 255, 255, 0.3);
    transform: translateY(-2px) scale(1.02);
}

.multi-agent-toggle .fas {
    font-size: 14px;
    transition: transform 0.3s ease;
}

.multi-agent-toggle:hover .fas {
    transform: rotate(5deg) scale(1.1);
}

.multi-agent-toggle.active .fas {
    animation: rotate-pulse 2s infinite ease-in-out;
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(0, 255, 255, 0.4), 0 0 40px rgba(0, 255, 255, 0.2);
    }
    50% {
        box-shadow: 0 0 25px rgba(0, 255, 255, 0.6), 0 0 50px rgba(0, 255, 255, 0.3);
    }
}

@keyframes rotate-pulse {
    0%, 100% {
        transform: rotate(0deg) scale(1);
    }
    25% {
        transform: rotate(5deg) scale(1.05);
    }
    75% {
        transform: rotate(-5deg) scale(1.05);
    }
}

.multi-agent-toggle i {
    font-size: 14px;
}

.toggle-text {
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Chat Container */
.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    display: flex;
    flex-direction: column;
}

.welcome-message {
    text-align: center;
    max-width: 600px;
    margin: auto;
    padding: 40px 20px;
}

.welcome-icon {
    font-size: 48px;
    color: #00FFFF;
    margin-bottom: 24px;
}

.robot-icon-img {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    filter: drop-shadow(0 4px 8px rgba(0, 255, 255, 0.3));
}

.welcome-message h2 {
    font-size: 32px;
    margin-bottom: 16px;
    font-weight: 600;
}

.welcome-message p {
    font-size: 18px;
    color: #b3b3b3;
    margin-bottom: 40px;
}

.example-prompts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-top: 32px;
}

.prompt-card {
    background-color: #2d2d2d;
    border: 1px solid #4d4d4d;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 12px;
    text-align: left;
}

.prompt-card:hover {
    background-color: #3d3d3d;
    border-color: #6d6d6d;
}

.prompt-card i {
    font-size: 20px;
    color: #00FFFF;
}

.prompt-card span {
    font-size: 14px;
    color: #ffffff;
}

/* Message Styles */
.message {
    margin-bottom: 24px;
    display: flex;
    gap: 16px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.user-avatar {
    background-color: transparent;
    color: transparent;
    border: none;
}

.ai-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.ai-avatar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 3s infinite;
}

.ai-avatar i {
    position: relative;
    z-index: 1;
    animation: brain-pulse 2s infinite ease-in-out;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes brain-pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

.message-content {
    flex: 1;
    line-height: 1.7;
    max-width: 100%;
    overflow-wrap: break-word;
    /* Enable text selection and copying */
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
    /* Ensure cursor shows as text selection cursor */
    cursor: text;
}

.user-message .message-content {
    background-color: #2d2d2d;
    padding: 12px 16px;
    border-radius: 12px;
    border-top-right-radius: 4px;
}

.ai-message .message-content {
    padding: 12px 0;
}

/* Ensure all text elements within messages are selectable */
.message-content *,
.message-content span,
.message-content p,
.message-content div,
.message-content code,
.message-content pre {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

/* HTML Formatting Styles for AI Messages */
.message-content h1,
.message-content h2,
.message-content h3 {
    font-size: 1.1em;
    color: #00FFFF;
    margin: 20px 0 12px 0;
    font-weight: 600;
    line-height: 1.4;
}

/* First header in message should have less top margin */
.message-content h1:first-child,
.message-content h2:first-child,
.message-content h3:first-child {
    margin-top: 0;
}

.message-content p {
    margin: 0 0 16px 0;
    line-height: 1.7;
}

.message-content p:last-child {
    margin-bottom: 0;
}

/* Better spacing for consecutive paragraphs */
.message-content p + p {
    margin-top: 8px;
}

.message-content ul,
.message-content ol {
    margin: 4px 0 20px 0;
    padding-left: 60px !important;
}

/* Reduced gap between headers and lists */
.message-content h1 + ul,
.message-content h1 + ol,
.message-content h2 + ul,
.message-content h2 + ol,
.message-content h3 + ul,
.message-content h3 + ol {
    margin-top: 4px;
}

.message-content li {
    margin: 8px 0;
    line-height: 1.5;
    padding: 0;
}

.message-content ul li {
    list-style-type: disc;
}

.message-content ol li {
    list-style-type: decimal;
}

/* Hide bullet points for list items that are likely headers/labels (handled by JavaScript) */
.message-content li.no-bullet {
    list-style-type: none;
    margin-left: -40px;
    font-weight: 600;
    color: #00FFFF;
    margin-bottom: 4px;
}

/* Better spacing for list items with nested content */
.message-content li p {
    margin: 2px 0;
    line-height: 1.5;
}

.message-content li:last-child {
    margin-bottom: 0;
}

/* Remove extra spacing from br tags in and around lists while preserving paragraph breaks */
.message-content ul br,
.message-content ol br,
.message-content ul > br,
.message-content ol > br,
.message-content li + br {
    display: none;
}

/* Allow br tags in paragraphs and other content for proper line breaks */

/* Enhanced Inline Code Styling */
.message-content code {
    background: linear-gradient(135deg, #2a2a2a 0%, #333333 100%);
    color: #00FFFF;
    padding: 3px 8px;
    border-radius: 6px;
    font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.88em;
    font-weight: 500;
    border: 1px solid #404040;
    box-shadow:
        0 1px 3px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    letter-spacing: 0.02em;
    position: relative;
    /* Fix for line wrapping - preserve styling across line breaks */
    box-decoration-break: clone;
    -webkit-box-decoration-break: clone;
}

.message-content code::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(0, 255, 255, 0.05) 50%, transparent 70%);
    border-radius: 6px;
    pointer-events: none;
}

/* Enhanced Code Block Styling */
.message-content pre {
    background: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
    border: 1px solid #333333;
    border-radius: 12px;
    padding: 20px;
    margin: 16px 0 20px 0;
    overflow-x: auto;
    max-width: 100%;
    position: relative;
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

/* Code block header with language indicator */
.message-content pre::before {
    content: 'Code';
    position: absolute;
    top: 0;
    right: 0;
    background: linear-gradient(135deg, #00FFFF, #00CCCC);
    color: #000000;
    font-size: 11px;
    font-weight: 600;
    padding: 4px 12px;
    border-radius: 0 12px 0 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Scrollbar styling for code blocks */
.message-content pre::-webkit-scrollbar {
    height: 8px;
}

.message-content pre::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 4px;
}

.message-content pre::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #00FFFF, #00CCCC);
    border-radius: 4px;
}

.message-content pre::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #00CCCC, #009999);
}

.message-content pre code {
    background: none;
    padding: 0;
    color: #f0f6fc;
    font-size: 0.9em;
    line-height: 1.7;
    font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace;
    font-weight: 400;
    letter-spacing: 0.02em;
    word-wrap: break-word;
    white-space: pre-wrap;
    border: none;
    box-shadow: none;
    display: block;
    overflow-x: auto;
    /* Ensure proper text rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

.message-content pre code::before {
    display: none;
}

/* Basic Syntax Highlighting for Common Keywords */
.message-content pre code {
    /* Keywords */
    background-image:
        /* Python keywords */
        linear-gradient(transparent, transparent),
        /* JavaScript keywords */
        linear-gradient(transparent, transparent),
        /* General keywords */
        linear-gradient(transparent, transparent);
}

/* Language-specific code block styling */
.message-content pre[data-language="python"]::before {
    content: 'Python';
    background: linear-gradient(135deg, #3776ab, #ffd43b);
    color: #ffffff;
}

.message-content pre[data-language="javascript"]::before,
.message-content pre[data-language="js"]::before {
    content: 'JavaScript';
    background: linear-gradient(135deg, #f7df1e, #f0db4f);
    color: #000000;
}

.message-content pre[data-language="html"]::before {
    content: 'HTML';
    background: linear-gradient(135deg, #e34c26, #f06529);
    color: #ffffff;
}

.message-content pre[data-language="css"]::before {
    content: 'CSS';
    background: linear-gradient(135deg, #1572b6, #33a9dc);
    color: #ffffff;
}

.message-content pre[data-language="json"]::before {
    content: 'JSON';
    background: linear-gradient(135deg, #000000, #333333);
    color: #ffffff;
}

.message-content pre[data-language="bash"]::before,
.message-content pre[data-language="shell"]::before {
    content: 'Shell';
    background: linear-gradient(135deg, #4eaa25, #89e051);
    color: #000000;
}

.message-content pre[data-language="sql"]::before {
    content: 'SQL';
    background: linear-gradient(135deg, #336791, #4479a1);
    color: #ffffff;
}

/* Copy button for code blocks - positioned beside language label */
.code-copy-btn {
    position: absolute;
    top: 0;
    right: 80px; /* Position to the left of the language label */
    background: linear-gradient(135deg, #2d2d2d, #404040);
    border: 1px solid #555555;
    border-radius: 0 0 0 8px; /* Match the language label styling */
    color: #00FFFF;
    padding: 4px 8px;
    font-size: 11px;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 28px;
    height: 23px; /* Match the height of language label */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.message-content pre:hover .code-copy-btn {
    opacity: 0.8;
}

.code-copy-btn:hover {
    opacity: 1 !important;
    background: linear-gradient(135deg, #00FFFF, #00CCCC);
    color: #000000;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
}

.code-copy-btn:active {
    transform: translateY(0);
}

.code-copy-btn i {
    font-size: 10px;
}

/* Improved code block animations */
.message-content pre {
    animation: codeBlockFadeIn 0.3s ease-out;
}

@keyframes codeBlockFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Code block glow effect on hover */
.message-content pre:hover {
    border-color: #404040;
    box-shadow:
        0 4px 20px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(0, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

/* Enhanced typography for code blocks */
.message-content pre code {
    /* Better font rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;

    /* Improved readability */
    tab-size: 4;
    -moz-tab-size: 4;
    -o-tab-size: 4;
}

/* Line numbers for code blocks (optional enhancement) */
.message-content pre.with-line-numbers {
    counter-reset: line-number;
    padding-left: 60px;
}

.message-content pre.with-line-numbers code {
    position: relative;
}

.message-content pre.with-line-numbers code::before {
    counter-increment: line-number;
    content: counter(line-number);
    position: absolute;
    left: -50px;
    top: 0;
    color: #666666;
    font-size: 0.8em;
    line-height: 1.6;
    text-align: right;
    width: 40px;
    user-select: none;
    pointer-events: none;
}

/* Responsive code blocks */
@media (max-width: 768px) {
    .message-content pre {
        padding: 16px;
        margin: 8px 0;
        border-radius: 8px;
        font-size: 0.85em;
    }

    .code-copy-btn {
        top: 0;
        right: 60px; /* Adjust for smaller screens */
        padding: 3px 6px;
        min-width: 24px;
        height: 20px;
        border-radius: 0 0 0 6px;
    }

    .code-copy-btn i {
        font-size: 9px;
    }

    .message-content pre::before {
        padding: 3px 8px;
        font-size: 9px;
    }
}

/* Dark theme optimizations */
.message-content pre {
    /* Better contrast for readability */
    background: linear-gradient(135deg, #0d1117 0%, #161b22 100%);
    border-color: #30363d;
}

.message-content pre code {
    color: #f0f6fc;
}

/* Enhanced Syntax highlighting colors */

/* CSS Syntax Highlighting */
.message-content pre code .css-selector {
    color: #ffa657;
    font-weight: 600;
}

.message-content pre code .css-property {
    color: #79c0ff;
}

.message-content pre code .css-value {
    color: #a5d6ff;
}

.message-content pre code .css-comment {
    color: #8b949e;
    font-style: italic;
}

/* JavaScript Syntax Highlighting */
.message-content pre code .js-keyword {
    color: #ff7b72;
    font-weight: 600;
}

.message-content pre code .js-string {
    color: #a5d6ff;
}

.message-content pre code .js-number {
    color: #79c0ff;
}

.message-content pre code .js-comment {
    color: #8b949e;
    font-style: italic;
}

/* Python Syntax Highlighting */
.message-content pre code .py-keyword {
    color: #ff7b72;
    font-weight: 600;
}

.message-content pre code .py-string {
    color: #a5d6ff;
}

.message-content pre code .py-number {
    color: #79c0ff;
}

.message-content pre code .py-comment {
    color: #8b949e;
    font-style: italic;
}

/* JSON Syntax Highlighting */
.message-content pre code .json-key {
    color: #79c0ff;
    font-weight: 600;
}

.message-content pre code .json-string {
    color: #a5d6ff;
}

.message-content pre code .json-number {
    color: #79c0ff;
}

.message-content pre code .json-literal {
    color: #ff7b72;
    font-weight: 600;
}

/* General syntax highlighting fallbacks */
.message-content pre code .keyword {
    color: #ff7b72;
    font-weight: 600;
}

.message-content pre code .string {
    color: #a5d6ff;
}

.message-content pre code .comment {
    color: #8b949e;
    font-style: italic;
}

.message-content pre code .number {
    color: #79c0ff;
}

.message-content pre code .function {
    color: #d2a8ff;
}

.message-content pre code .variable {
    color: #ffa657;
}

.message-content strong {
    color: #ffffff;
    font-weight: 600;
}

.message-content em {
    color: #00bcd4;
    font-style: italic;
}

/* Blockquote styling */
.message-content blockquote {
    border-left: 4px solid #00FFFF;
    margin: 20px 0;
    padding: 16px 0 16px 20px;
    background-color: rgba(0, 255, 255, 0.05);
    border-radius: 0 8px 8px 0;
    font-style: italic;
    color: #e0e0e0;
    line-height: 1.6;
}

.message-content blockquote p {
    margin: 0 0 12px 0;
    line-height: 1.6;
}

.message-content blockquote p:last-child {
    margin-bottom: 0;
}

/* Enhanced Horizontal rule styling */
.message-content hr {
    border: none;
    height: 2px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 255, 255, 0.1) 10%,
        rgba(0, 255, 255, 0.6) 30%,
        #00FFFF 50%,
        rgba(0, 255, 255, 0.6) 70%,
        rgba(0, 255, 255, 0.1) 90%,
        transparent 100%
    );
    margin: 32px 0;
    position: relative;
    border-radius: 2px;
    box-shadow:
        0 0 10px rgba(0, 255, 255, 0.3),
        0 2px 4px rgba(0, 0, 0, 0.2);
    animation: hr-glow 3s ease-in-out infinite alternate;
}

.message-content hr::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 255, 255, 0.05) 20%,
        rgba(0, 255, 255, 0.2) 50%,
        rgba(0, 255, 255, 0.05) 80%,
        transparent 100%
    );
    border-radius: 4px;
    filter: blur(2px);
}

.message-content hr::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: radial-gradient(circle, #00FFFF 0%, rgba(0, 255, 255, 0.3) 70%, transparent 100%);
    border-radius: 50%;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.8);
    animation: hr-pulse 2s ease-in-out infinite;
}

@keyframes hr-glow {
    0% {
        box-shadow:
            0 0 10px rgba(0, 255, 255, 0.3),
            0 2px 4px rgba(0, 0, 0, 0.2);
        filter: brightness(1);
    }
    100% {
        box-shadow:
            0 0 20px rgba(0, 255, 255, 0.5),
            0 4px 8px rgba(0, 0, 0, 0.3);
        filter: brightness(1.2);
    }
}

@keyframes hr-pulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 1;
    }
}

/* Table styling */
.message-content table {
    border-collapse: collapse;
    width: 100%;
    margin: 8px 0 4px 0;
    background-color: #2d2d2d;
    border-radius: 8px;
    overflow: hidden;
}

.message-content th,
.message-content td {
    border: 1px solid #4d4d4d;
    padding: 12px;
    text-align: left;
}

.message-content th {
    background-color: #3d3d3d;
    font-weight: 600;
    color: #00FFFF;
}

/* Add spacing between different content types */
.message-content > * + * {
    margin-top: 6px;
}

.message-content > *:first-child {
    margin-top: 0;
}

.message-content > *:last-child {
    margin-bottom: 0;
}

/* Special spacing for specific combinations */
.message-content > p + ul,
.message-content > p + ol {
    margin-top: 12px;
}

.message-content > ul + p,
.message-content > ol + p {
    margin-top: 16px;
}

/* Input Container */
.input-container {
    padding: 32px 24px;
    background: linear-gradient(180deg, #212121 0%, #1a1a1a 100%);
    position: relative;
}

.input-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #00FFFF 50%, transparent 100%);
    opacity: 0.3;
}

.input-wrapper {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 50%, #2a2a2a 100%);
    border-radius: 28px;
    border: 2px solid transparent;
    background-clip: padding-box;
    display: flex;
    align-items: flex-end;
    padding: 18px 24px;
    gap: 16px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
}

.input-wrapper::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(135deg, #4d4d4d 0%, #3a3a3a 50%, #4d4d4d 100%);
    border-radius: 28px;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask-composite: xor;
    transition: all 0.4s ease;
    pointer-events: none;
}

.input-buttons {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    padding-bottom: 2px;
}

.input-wrapper:hover {
    transform: translateY(-1px);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.5),
        0 4px 12px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.input-wrapper:hover::before {
    background: linear-gradient(135deg, #5d5d5d 0%, #4a4a4a 50%, #5d5d5d 100%);
}

.input-wrapper:focus-within {
    transform: translateY(-2px);
    box-shadow:
        0 16px 48px rgba(0, 0, 0, 0.6),
        0 0 0 4px rgba(0, 255, 255, 0.15),
        0 0 32px rgba(0, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.input-wrapper:focus-within::before {
    background: linear-gradient(135deg, #00FFFF 0%, #00CCCC 50%, #00FFFF 100%);
    animation: border-glow 2s ease-in-out infinite alternate;
}

@keyframes border-glow {
    0% {
        background: linear-gradient(135deg, #00FFFF 0%, #00CCCC 50%, #00FFFF 100%);
        opacity: 0.8;
    }
    100% {
        background: linear-gradient(135deg, #00CCCC 0%, #00FFFF 50%, #00CCCC 100%);
        opacity: 1;
    }
}

#messageInput {
    flex: 1;
    background: none;
    border: none;
    color: #ffffff;
    font-size: 18px;
    resize: none;
    outline: none;
    min-height: 32px;
    max-height: 140px;
    line-height: 1.6;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    padding: 12px 8px;
    border-radius: 28px;
    font-weight: 400;
    letter-spacing: 0.01em;
    transition: all 0.3s ease;
    vertical-align: bottom;
}

#messageInput::placeholder {
    color: #888888;
    font-style: normal;
    font-weight: 300;
    font-size: 18px;
    transition: all 0.3s ease;
}

#messageInput:focus::placeholder {
    color: #aaaaaa;
    transform: translateY(-1px);
}

/* Typing indicator for input */
.input-wrapper.typing {
    animation: subtle-pulse 2s ease-in-out infinite;
}

@keyframes subtle-pulse {
    0%, 100% {
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.4),
            0 2px 8px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.1),
            inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    }
    50% {
        box-shadow:
            0 12px 40px rgba(0, 0, 0, 0.5),
            0 4px 12px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.15),
            inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    }
}

.send-btn {
    background: linear-gradient(135deg, #00FFFF 0%, #00CCCC 50%, #0099CC 100%);
    border: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    color: #ffffff;
    font-size: 16px;
    box-shadow:
        0 4px 16px rgba(0, 255, 255, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.send-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
    transform: translateX(-100%) rotate(45deg);
    transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.send-btn:not(:disabled):hover::before {
    transform: translateX(100%) rotate(45deg);
}

.send-btn::after {
    content: '';
    position: absolute;
    inset: 2px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(0, 0, 0, 0.1) 100%);
    pointer-events: none;
}

.send-btn:disabled {
    background: linear-gradient(135deg, #4a4a4a 0%, #3a3a3a 50%, #2a2a2a 100%);
    cursor: not-allowed;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    color: #666666;
}

.send-btn:not(:disabled):hover {
    background: linear-gradient(135deg, #00CCCC 0%, #009999 50%, #007777 100%);
    transform: translateY(-2px) scale(1.05);
    box-shadow:
        0 8px 24px rgba(0, 255, 255, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.send-btn:not(:disabled):active {
    transform: translateY(-1px) scale(1.02);
    box-shadow:
        0 4px 16px rgba(0, 255, 255, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

/* Upload Button Styles */
.upload-btn {
    background: linear-gradient(135deg, #6c5ce7 0%, #5a4fcf 50%, #4834d4 100%);
    border: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    color: white;
    font-size: 16px;
    box-shadow:
        0 4px 16px rgba(108, 92, 231, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.upload-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
    transform: translateX(-100%) rotate(45deg);
    transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-btn:hover::before {
    transform: translateX(100%) rotate(45deg);
}

.upload-btn::after {
    content: '';
    position: absolute;
    inset: 2px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(0, 0, 0, 0.1) 100%);
    pointer-events: none;
}

.upload-btn:hover {
    background: linear-gradient(135deg, #5a4fcf 0%, #4834d4 50%, #3742fa 100%);
    transform: translateY(-2px) scale(1.05);
    box-shadow:
        0 8px 24px rgba(108, 92, 231, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.upload-btn:active {
    transform: translateY(-1px) scale(1.02);
    box-shadow:
        0 4px 16px rgba(108, 92, 231, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.upload-btn.processing {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 50%, #d35400 100%);
    animation: processing-pulse 2s ease-in-out infinite;
}

@keyframes processing-pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow:
            0 4px 16px rgba(243, 156, 18, 0.4),
            0 2px 8px rgba(0, 0, 0, 0.2);
    }
    50% {
        transform: scale(1.08);
        box-shadow:
            0 8px 24px rgba(243, 156, 18, 0.6),
            0 4px 12px rgba(0, 0, 0, 0.3);
    }
}

/* File upload drag and drop styles */
.input-wrapper.drag-over {
    transform: translateY(-3px) scale(1.02);
    background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 50%, #2a2a2a 100%);
    box-shadow:
        0 16px 48px rgba(108, 92, 231, 0.3),
        0 8px 24px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.input-wrapper.drag-over::before {
    background: linear-gradient(135deg, #6c5ce7 0%, #5a4fcf 50%, #6c5ce7 100%);
    animation: drag-glow 1.5s ease-in-out infinite alternate;
}

@keyframes drag-glow {
    0% {
        opacity: 0.8;
        background: linear-gradient(135deg, #6c5ce7 0%, #5a4fcf 50%, #6c5ce7 100%);
    }
    100% {
        opacity: 1;
        background: linear-gradient(135deg, #5a4fcf 0%, #6c5ce7 50%, #5a4fcf 100%);
    }
}

/* Document Analysis Display Styles */
.document-analysis-display {
    background: rgba(108, 92, 231, 0.1);
    border: 1px solid rgba(108, 92, 231, 0.3);
    border-radius: 8px;
    padding: 12px;
    margin-top: 12px;
    font-size: 12px;
}

.document-analysis-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
    color: #6c5ce7;
    margin-bottom: 8px;
}

.document-analysis-header i {
    font-size: 14px;
}

.document-analysis-list {
    color: #b3b3b3;
    line-height: 1.4;
    font-size: 11px;
}

.document-analysis-list strong {
    color: #ffffff;
}

/* File type indicator */
.file-type-indicator {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #00FFFF 0%, #00CCCC 100%);
    color: #1a1a1a;
    font-size: 10px;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 255, 255, 0.3);
    display: none;
}

.upload-btn.has-files .file-type-indicator {
    display: block;
}



.input-footer {
    text-align: center;
    margin-top: 12px;
}

.input-footer small {
    color: #8d8d8d;
    font-size: 12px;
}

/* Responsive Design */

/* Large tablets and small desktops */
@media (max-width: 1024px) {
    .sidebar {
        width: 280px;
    }

    .message {
        max-width: 100%;
        padding: 0 16px;
    }

    .input-container {
        padding: 24px 16px;
    }

    .input-wrapper {
        max-width: 100%;
        padding: 16px 20px;
        gap: 12px;
    }

    .header {
        padding: 16px 20px;
    }

    .chat-container {
        padding: 20px 16px;
    }
}

/* Tablets */
@media (max-width: 768px) {
    .sidebar-backdrop {
        display: block;
    }

    .sidebar {
        position: fixed;
        left: 0;
        top: 0;
        height: var(--app-height);
        width: 280px;
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        /* Ensure sidebar doesn't get cut off */
        max-height: var(--app-height);
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        /* Improve performance for animations */
        will-change: transform;
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .sidebar.hidden {
        transform: translateX(-100%);
    }

    .main-content {
        width: 100%;
        margin-left: 0;
    }

    .main-content.sidebar-hidden {
        margin-left: 0;
    }

    .header {
        padding: 12px 16px;
    }

    .app-title {
        font-size: 16px;
    }

    .header-actions {
        gap: 6px;
    }

    .multi-agent-toggle {
        padding: 4px 8px;
        font-size: 11px;
    }

    .multi-agent-toggle .toggle-text {
        display: none;
    }

    .example-prompts {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .prompt-card {
        padding: 16px;
        font-size: 13px;
    }

    .welcome-message {
        padding: 30px 16px;
        max-width: 100%;
    }

    .welcome-message h2 {
        font-size: 24px;
    }

    .welcome-message p {
        font-size: 16px;
    }

    .message {
        gap: 12px;
        margin-bottom: 20px;
    }

    .message-content {
        font-size: 15px;
        line-height: 1.6;
    }

    .input-container {
        padding: 20px 16px;
    }

    .input-wrapper {
        padding: 14px 16px;
        gap: 10px;
        border-radius: 24px;
    }

    #messageInput {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 10px 6px;
    }

    .send-btn, .upload-btn {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }
}

/* Large mobile phones */
@media (max-width: 640px) {
    .header {
        padding: 10px 12px;
    }

    .sidebar {
        width: 260px;
    }

    .chat-container {
        padding: 16px 12px;
    }

    .message {
        gap: 10px;
        margin-bottom: 16px;
    }

    .message-avatar {
        width: 28px;
        height: 28px;
        font-size: 14px;
    }

    .user-message .message-content {
        padding: 10px 14px;
        border-radius: 10px;
        font-size: 14px;
    }

    .ai-message .message-content {
        font-size: 14px;
    }

    .input-container {
        padding: 16px 12px;
    }

    .input-wrapper {
        padding: 12px 14px;
        gap: 8px;
        border-radius: 20px;
    }

    #messageInput {
        font-size: 16px;
        padding: 8px 4px;
        min-height: 28px;
    }

    .send-btn, .upload-btn {
        width: 36px;
        height: 36px;
        font-size: 13px;
    }

    .welcome-message {
        padding: 24px 12px;
    }

    .welcome-message h2 {
        font-size: 22px;
        margin-bottom: 12px;
    }

    .welcome-message p {
        font-size: 15px;
        margin-bottom: 30px;
    }

    .robot-icon-img {
        width: 50px;
        height: 50px;
    }

    .example-prompts {
        gap: 10px;
        margin-top: 24px;
    }

    .prompt-card {
        padding: 14px;
        font-size: 12px;
    }

    .prompt-card i {
        font-size: 16px;
    }
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
    background: #4d4d4d;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #6d6d6d;
}

/* AI Thinking Animation */
.ai-thinking-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
}

/* Enhanced Brain Icon Container */
.agent-brain-container {
    position: relative;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.5s ease;
}

.agent-brain-icon {
    font-size: 18px;
    color: #ffffff;
    transition: all 0.3s ease;
    z-index: 2;
    position: relative;
}

.agent-brain-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.agent-brain-overlay i {
    font-size: 18px;
    color: #ffffff;
    opacity: 0.3;
}

/* Agent Brain Container States */
.agent-brain-container.orchestrator {
    background: linear-gradient(135deg, #667eea, #764ba2);
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
    animation: brain-pulse-orchestrator 2s infinite ease-in-out;
}

.agent-brain-container.project_manager {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    box-shadow: 0 0 20px rgba(240, 147, 251, 0.4);
    animation: brain-pulse-project 2s infinite ease-in-out;
}

.agent-brain-container.planner {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    box-shadow: 0 0 20px rgba(79, 172, 254, 0.4);
    animation: brain-pulse-planner 2s infinite ease-in-out;
}

.agent-brain-container.sales {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    box-shadow: 0 0 20px rgba(67, 233, 123, 0.4);
    animation: brain-pulse-sales 2s infinite ease-in-out;
}

.agent-brain-container.coding {
    background: linear-gradient(135deg, #fa709a, #fee140);
    box-shadow: 0 0 20px rgba(250, 112, 154, 0.4);
    animation: brain-pulse-coding 2s infinite ease-in-out;
}

.agent-brain-container.validation {
    background: linear-gradient(135deg, #a8edea, #fed6e3);
    box-shadow: 0 0 20px rgba(168, 237, 234, 0.4);
    animation: brain-pulse-validation 2s infinite ease-in-out;
}

.agent-brain-container.transitioning {
    animation: brain-transition 0.6s ease-in-out;
}



.agent-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #ffffff;
    position: relative;
    transition: all 0.5s ease;
}

.agent-icon.orchestrator {
    background: linear-gradient(135deg, #667eea, #764ba2);
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
    animation: agent-pulse-orchestrator 2s infinite ease-in-out;
}

.agent-icon.project_manager {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    box-shadow: 0 0 20px rgba(240, 147, 251, 0.4);
    animation: agent-pulse-project 2s infinite ease-in-out;
}

.agent-icon.planner {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    box-shadow: 0 0 20px rgba(79, 172, 254, 0.4);
    animation: agent-pulse-planner 2s infinite ease-in-out;
}

.agent-icon.sales {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    box-shadow: 0 0 20px rgba(67, 233, 123, 0.4);
    animation: agent-pulse-sales 2s infinite ease-in-out;
}

.agent-icon.coding {
    background: linear-gradient(135deg, #fa709a, #fee140);
    box-shadow: 0 0 20px rgba(250, 112, 154, 0.4);
    animation: agent-pulse-coding 2s infinite ease-in-out;
}

.agent-icon.validation {
    background: linear-gradient(135deg, #a8edea, #fed6e3);
    box-shadow: 0 0 20px rgba(168, 237, 234, 0.4);
    animation: agent-pulse-validation 2s infinite ease-in-out;
}

.agent-icon.project_manager {
    background: linear-gradient(135deg, #f093fb, #f5576c);
    box-shadow: 0 0 20px rgba(240, 147, 251, 0.4);
}

.agent-icon.planner {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
    box-shadow: 0 0 20px rgba(79, 172, 254, 0.4);
}

.agent-icon.sales {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    box-shadow: 0 0 20px rgba(67, 233, 123, 0.4);
}

.agent-icon.coding {
    background: linear-gradient(135deg, #fa709a, #fee140);
    box-shadow: 0 0 20px rgba(250, 112, 154, 0.4);
}

.agent-icon.validation {
    background: linear-gradient(135deg, #a8edea, #fed6e3);
    box-shadow: 0 0 20px rgba(168, 237, 234, 0.4);
}

.agent-name {
    font-size: 12px;
    font-weight: 600;
    color: #00FFFF;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    animation: agent-text-glow 2s infinite ease-in-out;
    transition: opacity 0.3s ease;
}

.agent-status {
    font-size: 11px;
    color: #b3b3b3;
    font-style: italic;
    transition: opacity 0.3s ease;
}

/* Brain Pulse Animations for Each Agent */
@keyframes brain-pulse-orchestrator {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
    }
}

@keyframes brain-pulse-project {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(240, 147, 251, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 30px rgba(240, 147, 251, 0.6);
    }
}

@keyframes brain-pulse-planner {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(79, 172, 254, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 30px rgba(79, 172, 254, 0.6);
    }
}

@keyframes brain-pulse-sales {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(67, 233, 123, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 30px rgba(67, 233, 123, 0.6);
    }
}

@keyframes brain-pulse-coding {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(250, 112, 154, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 30px rgba(250, 112, 154, 0.6);
    }
}

@keyframes brain-pulse-validation {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(168, 237, 234, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 30px rgba(168, 237, 234, 0.6);
    }
}

@keyframes brain-transition {
    0% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(0.8) rotate(180deg);
        opacity: 0.5;
    }
    100% {
        transform: scale(1) rotate(360deg);
        opacity: 1;
    }
}

/* Agent Icon Pulse Animations */
@keyframes agent-pulse-orchestrator {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
    }
}

@keyframes agent-pulse-project {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(240, 147, 251, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 30px rgba(240, 147, 251, 0.6);
    }
}

@keyframes agent-pulse-planner {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(79, 172, 254, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 30px rgba(79, 172, 254, 0.6);
    }
}

@keyframes agent-pulse-sales {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(67, 233, 123, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 30px rgba(67, 233, 123, 0.6);
    }
}

@keyframes agent-pulse-coding {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(250, 112, 154, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 30px rgba(250, 112, 154, 0.6);
    }
}

@keyframes agent-pulse-validation {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(168, 237, 234, 0.4);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 30px rgba(168, 237, 234, 0.6);
    }
}

@keyframes agent-text-glow {
    0%, 100% {
        text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
    }
    50% {
        text-shadow: 0 0 15px rgba(0, 255, 255, 0.8);
    }
}

/* Agent transition animation */
.agent-icon.transitioning {
    animation: agent-transition 0.6s ease-in-out;
}

@keyframes agent-transition {
    0% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(0.8) rotate(180deg);
        opacity: 0.5;
    }
    100% {
        transform: scale(1) rotate(360deg);
        opacity: 1;
    }
}

@keyframes agent-transition {
    0% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    25% {
        transform: scale(0.8) rotate(90deg);
        opacity: 0.7;
    }
    50% {
        transform: scale(0.6) rotate(180deg);
        opacity: 0.3;
    }
    75% {
        transform: scale(0.8) rotate(270deg);
        opacity: 0.7;
    }
    100% {
        transform: scale(1) rotate(360deg);
        opacity: 1;
    }
}

.neural-pulse {
    width: 40px;
    height: 14px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.neural-pulse::before,
.neural-pulse::after {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    animation: neural-pulse 2s infinite ease-in-out;
    position: relative;
    top: 0;
}

.neural-pulse::before {
    animation-delay: 0s;
}

.neural-pulse::after {
    animation-delay: 0.5s;
}

.thinking-text {
    color: #a0a0a0;
    font-size: 14px;
    font-style: italic;
    animation: fade-pulse 2s infinite ease-in-out;
    line-height: 14px;
    margin: 0;
    padding: 0;
}

@keyframes neural-pulse {
    0%, 100% {
        transform: scale(0.8);
        opacity: 0.4;
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
}

@keyframes fade-pulse {
    0%, 100% {
        opacity: 0.6;
    }
    50% {
        opacity: 1;
    }
}

/* Multi-Agent Progress Indicator */
.multi-agent-progress {
    margin-top: 8px;
    padding: 8px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.progress-text {
    color: #00FFFF;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 6px;
    /* animation: agent-pulse 1.5s infinite ease-in-out; */
}

.other-agents-thinking {
    margin-top: 8px;
    padding-top: 6px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.other-agent-item {
    color: rgba(255, 255, 255, 0.4);
    font-size: 10px;
    font-weight: 400;
    margin-bottom: 3px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.other-agent-item:last-child {
    margin-bottom: 0;
}

.other-agent-dots {
    display: flex;
    gap: 2px;
}

.other-agent-dots span {
    width: 2px;
    height: 2px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    animation: thinking-dots 1.4s infinite ease-in-out;
}

.other-agent-dots span:nth-child(1) {
    animation-delay: 0s;
}

.other-agent-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.other-agent-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

/* Agent Thinking Status */
.agent-thinking-status {
    margin-bottom: 16px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease-in-out;
}

.agent-thinking-status.active {
    opacity: 1;
    transform: translateY(0);
}

/* Discussion Status */
.discussion-status {
    margin-bottom: 16px;
    padding: 8px 12px;
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 6px;
    text-align: center;
}

.discussion-status-text {
    font-size: 12px;
    color: #00FFFF;
    font-weight: 500;
}

/* Temporary Agent Response Display */
.temporary-agent-response {
    background: rgb(31, 31, 31);  /* Purple background */
    border: 1px solid rgb(255, 255, 255);  /* Purple border */
    border-radius: 8px;
    padding: 12px;
    margin-top: 12px;
    backdrop-filter: blur(5px);
    animation: slideInFromTop 0.3s ease-out;
    transition: opacity 0.3s ease-in-out;
}

.temporary-agent-response.fade-out {
    opacity: 0;
    transform: translateY(-10px);
}

.agent-response-header {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.agent-name {
    color: #0099ffe1;  /* Purple agent name */
    font-size: 14px;
    font-weight: 600;
}

.agent-task {
    color: rgba(255, 255, 255, 0.8);
    font-size: 11px;
    font-style: italic;
}

.agent-timestamp {
    color: rgba(255, 255, 255, 0.5);
    font-size: 10px;
}

/* Agent Pills Container */
.agent-pills-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 12px;
    padding-top: 8px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.agent-pill {
    background: linear-gradient(135deg, rgba(0, 153, 255, 0.2), rgba(0, 153, 255, 0.1));
    border: 1px solid rgba(0, 153, 255, 0.3);
    border-radius: 20px;
    padding: 6px 12px;
    font-size: 11px;
    font-weight: 500;
    color: #0099ff;
    backdrop-filter: blur(5px);
    animation: pillFadeIn 0.4s ease-out forwards;
    opacity: 0;
    transform: translateY(10px) scale(0.9);
    transition: all 0.2s ease;
}

.agent-pill:hover {
    background: linear-gradient(135deg, rgba(0, 153, 255, 0.3), rgba(0, 153, 255, 0.2));
    border-color: rgba(0, 153, 255, 0.5);
    transform: translateY(0) scale(1.02);
}

@keyframes pillFadeIn {
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.agent-response-content {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    line-height: 1.4;
    word-wrap: break-word;
}

.animated-text span {
    display: inline;
    opacity: 0;
    transition: opacity 0.7s ease-in;
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Discussion Status Text */
.status-text {
    color: #00FFFF;
    font-size: 13px;
    font-weight: 600;
    text-align: center;
    padding: 8px 16px;
    border-radius: 6px;
    background: rgba(0, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    margin-bottom: 12px;
    transition: all 0.5s ease;
    animation: status-glow 2s infinite ease-in-out;
    backdrop-filter: blur(5px);
}

@keyframes status-glow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(0, 255, 255, 0.2);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 20px rgba(0, 255, 255, 0.4);
        transform: scale(1.02);
    }
}

.progress-bar-container {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #00FFFF, #FF00FF, #00FFFF);
    background-size: 200% 100%;
    border-radius: 2px;
    transition: width 0.3s ease;
    animation: progress-shimmer 2s infinite linear;
}

@keyframes agent-pulse {
    0%, 100% {
        opacity: 0.8;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.02);
    }
}

@keyframes progress-shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Simplified typing cursor - no gradient effects */
.message-content.typing::after {
    display: none; /* Remove the gradient cursor completely */
}

/* Remove all dynamic cursor styles */
.message-content.typing.cursor-style-1::after,
.message-content.typing.cursor-style-2::after,
.message-content.typing.cursor-style-3::after {
    display: none; /* Remove all gradient cursor variants */
}

/* Cursor animations removed - no longer needed */

/* Text shimmer animation removed - no longer needed */

/* Enhanced text effects for cinematic typing */
.message-content.typing {
    position: relative;
    overflow: hidden;
}

/* Shimmer wave effect disabled */
.message-content.typing::before {
    display: none;
}

@keyframes typing-shimmer {
    /* Animation disabled */
    0%, 100% {
        opacity: 0;
    }
}

/* Word fade-in effect for newly typed words (legacy - now using char-based) */
.message-content.typing .word-fade-in {
    animation: word-appear 0.3s ease-out;
    /* Ensure text selection works on animated spans */
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

@keyframes word-appear {
    0% {
        opacity: 0;
        transform: translateY(8px) scale(0.9);
        filter: blur(1px);
    }
    50% {
        opacity: 0.8;
        transform: translateY(4px) scale(1.05);
        filter: blur(0.5px);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0);
    }
}

/* Character fade-in effect for character-by-character typing (1 char per 1ms) */
.message-content.typing .char-fade-in {
    animation: char-appear 0.3s ease-out;
    /* Ensure text selection works on animated spans */
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

@keyframes char-appear {
    0% {
        opacity: 0;
        transform: translateY(10px) scale(0.8);
        filter: blur(2px);
    }
    50% {
        opacity: 0.7;
        transform: translateY(5px) scale(1.1);
        filter: blur(1px);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0);
    }
}

/* Gradient text effect for special use cases (currently disabled for regular typing) */
.gradient-text {
    background: linear-gradient(45deg, #00FFFF, #FF00FF, #FFFF00);
    background-size: 200% 200%;
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    animation: gradient-flow 2s ease-in-out infinite;
}

@keyframes gradient-flow {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* API Setup Notification */
.api-setup-notification {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #00FFFF, #00CCCC);
    color: #ffffff;
    z-index: 9999;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    animation: slideDown 0.3s ease-out;
}

.notification-content {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    max-width: 1200px;
    margin: 0 auto;
    gap: 16px;
}

.notification-content i.fa-info-circle {
    font-size: 24px;
    color: #ffffff;
    flex-shrink: 0;
}

.notification-text {
    flex: 1;
}

.notification-text strong {
    display: block;
    font-size: 16px;
    margin-bottom: 4px;
}

.notification-text p {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
}

.notification-text a {
    color: #ffffff;
    text-decoration: underline;
    font-weight: 500;
}

.notification-text a:hover {
    text-decoration: none;
}

.notification-close {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
    flex-shrink: 0;
}

.notification-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}



/* Authentication Styles */
.auth-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.auth-background {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 255, 255, 0.05) 0%, transparent 50%);
}

.auth-card {
    background: rgba(33, 33, 33, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid #4d4d4d;
    border-radius: 16px;
    padding: 40px;
    width: 100%;
    max-width: 420px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-header {
    text-align: center;
    margin-bottom: 32px;
}

.auth-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 8px;
}

.auth-logo i {
    font-size: 32px;
    color: #00FFFF;
}

.auth-logo h1 {
    font-size: 28px;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
}

.auth-subtitle {
    color: #8d8d8d;
    font-size: 14px;
    margin: 0;
}

.auth-tabs {
    display: flex;
    background-color: #2d2d2d;
    border-radius: 8px;
    padding: 4px;
    margin-bottom: 24px;
}

.auth-tab {
    flex: 1;
    background: none;
    border: none;
    color: #b3b3b3;
    font-size: 14px;
    font-weight: 500;
    padding: 12px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
}

.auth-tab.active {
    background-color: #00FFFF;
    color: #ffffff;
}

.auth-tab:not(.active):hover {
    background-color: #3d3d3d;
    color: #ffffff;
}

.auth-form {
    display: block;
}

.auth-form.hidden {
    display: none;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.form-group input {
    width: 100%;
    background-color: #2d2d2d;
    border: 1px solid #4d4d4d;
    border-radius: 8px;
    padding: 12px 16px;
    color: #ffffff;
    font-size: 16px;
    transition: all 0.2s;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #00FFFF;
    box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.1);
}

.form-group input::placeholder {
    color: #8d8d8d;
}

.auth-btn {
    width: 100%;
    background: linear-gradient(135deg, #00FFFF, #00CCCC);
    border: none;
    border-radius: 8px;
    padding: 14px 20px;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 16px;
}

.auth-btn:hover {
    background: linear-gradient(135deg, #00CCCC, #009999);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
}

.auth-btn:active {
    transform: translateY(0);
}

.auth-links {
    text-align: center;
}

.forgot-password {
    color: #00FFFF;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s;
}

.forgot-password:hover {
    color: #00CCCC;
    text-decoration: underline;
}

.terms-text {
    color: #8d8d8d;
    font-size: 12px;
    margin: 0;
    line-height: 1.4;
}

.auth-footer {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #2d2d2d;
    text-align: center;
}

.auth-footer .powered-by {
    color: #8d8d8d;
}

.auth-footer .powered-by small {
    display: block;
    font-size: 11px;
    margin-bottom: 4px;
}

.auth-footer .ai-branding {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 12px;
    color: #00FFFF;
}

.auth-footer .ai-branding i {
    font-size: 14px;
}

/* Small mobile phones */
@media (max-width: 480px) {
    body {
        overflow-x: hidden;
    }

    .header {
        padding: 8px 10px;
        min-height: 50px;
    }

    .app-title {
        font-size: 14px;
        font-weight: 500;
    }

    .sidebar {
        width: 240px;
    }

    .sidebar-header {
        padding: 12px;
    }

    .new-chat-btn {
        padding: 10px 12px;
        font-size: 13px;
    }

    .chat-container {
        padding: 12px 8px;
    }

    .message {
        gap: 8px;
        margin-bottom: 14px;
    }

    .message-avatar {
        width: 24px;
        height: 24px;
        font-size: 12px;
    }

    .user-message .message-content {
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 13px;
        line-height: 1.5;
    }

    .ai-message .message-content {
        font-size: 13px;
        line-height: 1.5;
    }

    .input-container {
        padding: 12px 8px;
    }

    .input-wrapper {
        padding: 10px 12px;
        gap: 6px;
        border-radius: 18px;
        max-width: 100%;
        margin: 0;
    }

    #messageInput {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 6px 2px;
        min-height: 24px;
        max-height: 120px;
    }

    #messageInput::placeholder {
        font-size: 15px;
    }

    .send-btn, .upload-btn {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }

    .welcome-message {
        padding: 20px 8px;
    }

    .welcome-message h2 {
        font-size: 20px;
        margin-bottom: 10px;
    }

    .welcome-message p {
        font-size: 14px;
        margin-bottom: 24px;
    }

    .robot-icon-img {
        width: 40px;
        height: 40px;
    }

    .example-prompts {
        gap: 8px;
        margin-top: 20px;
    }

    .prompt-card {
        padding: 12px;
        font-size: 11px;
        border-radius: 8px;
    }

    .prompt-card i {
        font-size: 14px;
    }

    /* Improve code blocks on mobile */
    .message-content pre {
        padding: 12px;
        margin: 6px 0;
        border-radius: 6px;
        font-size: 0.8em;
        overflow-x: auto;
    }

    .message-content code {
        font-size: 0.8em;
        padding: 2px 6px;
        border-radius: 4px;
    }

    /* Better list spacing on mobile */
    .message-content ul,
    .message-content ol {
        padding-left: 40px !important;
        margin: 2px 0 16px 0;
    }

    .message-content li {
        margin: 6px 0;
        font-size: 13px;
        line-height: 1.4;
    }

    /* Settings menu mobile optimization */
    .settings-content {
        width: 95%;
        max-width: none;
        margin: 10px;
        max-height: 90vh;
    }

    .settings-header {
        padding: 16px 20px;
    }

    .settings-section {
        padding: 20px;
    }

    .settings-actions {
        padding: 16px 20px;
        flex-direction: column;
        gap: 8px;
    }

    .settings-btn {
        width: 100%;
        padding: 12px 20px;
    }
}

/* Responsive Auth Design */
@media (max-width: 480px) {
    .auth-card {
        margin: 20px;
        padding: 32px 24px;
        max-width: none;
    }

    .auth-logo h1 {
        font-size: 24px;
    }

    .auth-logo i {
        font-size: 28px;
    }
}

/* Loading Screen Styles */
.loading-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease-in-out;
}

.loading-container.fade-out {
    opacity: 0;
}

.loading-content {
    text-align: center;
    max-width: 400px;
    padding: 40px;
}

.loading-logo {
    margin-bottom: 40px;
}

.loading-logo i {
    font-size: 64px;
    color: #00FFFF;
    margin-bottom: 16px;
    display: block;
    animation: pulse 2s infinite;
}

.loading-logo h2 {
    font-size: 32px;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
}

.loading-spinner {
    margin: 40px 0;
    display: flex;
    justify-content: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #2d2d2d;
    border-top: 4px solid #00FFFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    margin: 30px 0;
}

.loading-text p {
    font-size: 18px;
    color: #b3b3b3;
    margin: 0;
    animation: fadeInOut 2s infinite;
}

.loading-progress {
    margin-top: 30px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background-color: #2d2d2d;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 12px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00FFFF, #00CCCC);
    border-radius: 3px;
    width: 0%;
    transition: width 0.3s ease;
    animation: shimmer 2s infinite;
}

.progress-text {
    font-size: 14px;
    color: #888;
    font-weight: 500;
}

/* Agent Thinking Status Box */
.agent-thinking-status {
    margin-top: 12px;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.4s ease;
}

.agent-thinking-status.active {
    opacity: 1;
    transform: translateY(0);
}

.thinking-box {
    background: rgba(0, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 8px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    animation: thinking-glow 2s infinite ease-in-out;
}

.thinking-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    animation: thinking-shimmer 2s infinite;
}

.thinking-agent-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #00FFFF, #FF00FF);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    animation: thinking-pulse 1.5s infinite ease-in-out;
}

.thinking-agent-icon i {
    font-size: 12px;
    color: white;
}

.thinking-content {
    flex: 1;
    min-width: 0;
}

.thinking-agent-name {
    font-size: 12px;
    font-weight: 600;
    color: #00FFFF;
    margin-bottom: 2px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.thinking-text {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    animation: thinking-text-fade 3s infinite ease-in-out;
}

.typing-cursor {
    color: #00FFFF;
    animation: cursor-blink 1s infinite;
    font-weight: bold;
}

.thinking-dots {
    display: flex;
    gap: 3px;
    align-items: center;
}

.thinking-dots span {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #00FFFF;
    animation: thinking-dots 1.4s infinite ease-in-out;
}

.thinking-dots span:nth-child(1) {
    animation-delay: 0s;
}

.thinking-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.thinking-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

/* Animations */
@keyframes thinking-glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(0, 255, 255, 0.2);
    }
    50% {
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.4);
    }
}

@keyframes thinking-shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

@keyframes thinking-pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

@keyframes thinking-text-fade {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
}

@keyframes thinking-dots {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

@keyframes cursor-blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

/* Loading Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Responsive Loading Screen */
@media (max-width: 480px) {
    .loading-content {
        padding: 20px;
        max-width: 300px;
    }

    .loading-logo i {
        font-size: 48px;
    }

    .loading-logo h2 {
        font-size: 24px;
    }

    .loading-text p {
        font-size: 16px;
    }

    .spinner {
        width: 40px;
        height: 40px;
    }
}

/* Notification Toast */
.notification-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(30, 30, 30, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 16px;
    max-width: 400px;
    z-index: 10000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.notification-toast.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-toast.notification-success {
    border-color: rgba(0, 255, 0, 0.5);
    background: rgba(0, 50, 0, 0.9);
}

.notification-toast.notification-info {
    border-color: rgba(0, 255, 255, 0.5);
    background: rgba(0, 50, 50, 0.9);
}

.notification-toast.notification-warning {
    border-color: rgba(255, 255, 0, 0.5);
    background: rgba(50, 50, 0, 0.9);
}

.notification-toast.notification-error {
    border-color: rgba(255, 0, 0, 0.5);
    background: rgba(50, 0, 0, 0.9);
}

.notification-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.notification-message {
    flex: 1;
    color: #ffffff;
    font-size: 14px;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: #b3b3b3;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;
    flex-shrink: 0;
}

.notification-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

/* Very small mobile phones */
@media (max-width: 360px) {
    .header {
        padding: 6px 8px;
        min-height: 45px;
    }

    .app-title {
        font-size: 13px;
    }

    .sidebar {
        width: 220px;
    }

    .chat-container {
        padding: 10px 6px;
    }

    .input-container {
        padding: 10px 6px;
    }

    .input-wrapper {
        padding: 8px 10px;
        gap: 4px;
        border-radius: 16px;
    }

    #messageInput {
        font-size: 16px;
        padding: 4px 2px;
        min-height: 20px;
    }

    .send-btn, .upload-btn {
        width: 28px;
        height: 28px;
        font-size: 11px;
    }

    .welcome-message {
        padding: 16px 6px;
    }

    .welcome-message h2 {
        font-size: 18px;
    }

    .welcome-message p {
        font-size: 13px;
    }

    .robot-icon-img {
        width: 35px;
        height: 35px;
    }

    .prompt-card {
        padding: 10px;
        font-size: 10px;
    }

    .prompt-card i {
        font-size: 12px;
    }

    .message-content {
        font-size: 12px;
    }

    .user-message .message-content {
        font-size: 12px;
        padding: 6px 10px;
    }
}

/* Landscape orientation optimizations */
@media (max-height: 500px) and (orientation: landscape) {
    .welcome-message {
        padding: 16px 20px;
    }

    .welcome-message h2 {
        font-size: 20px;
        margin-bottom: 8px;
    }

    .welcome-message p {
        margin-bottom: 16px;
    }

    .example-prompts {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        margin-top: 16px;
    }

    .input-container {
        padding: 12px 16px;
    }

    .robot-icon-img {
        width: 40px;
        height: 40px;
    }
}

/* Responsive adjustments for notifications */
@media (max-width: 768px) {
    .notification-toast {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* Mobile Browser Viewport Fixes */
@media (max-width: 768px) {
    /* Fix for Samsung Browser and other mobile browsers */
    html {
        height: var(--app-height);
        height: -webkit-fill-available;
    }

    body {
        height: var(--app-height);
        height: -webkit-fill-available;
        /* Prevent address bar from affecting layout */
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        overflow: hidden;
    }

    .app-container {
        height: var(--app-height);
        height: -webkit-fill-available;
        /* Ensure full height on mobile browsers */
        min-height: var(--app-height);
        min-height: -webkit-fill-available;
        position: relative;
        overflow: hidden;
    }

    .main-content {
        height: var(--app-height);
        height: -webkit-fill-available;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .chat-container {
        flex: 1;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        /* Ensure proper scrolling on mobile */
        height: 0; /* Allow flex to control height */
        min-height: 0;
    }

    /* Allow text selection in message content */
    .message-content {
        -webkit-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        user-select: text;
    }

    /* Improve touch targets */
    .chat-item,
    .new-chat-btn,
    .prompt-card,
    .auth-btn,
    .settings-btn {
        min-height: 44px; /* iOS recommended touch target size */
        display: flex;
        align-items: center;
    }

    /* Prevent zoom on input focus (iOS) */
    input, textarea, select {
        font-size: 16px !important;
    }

    /* Improve scrolling performance */
    .chat-container,
    .chat-history,
    .settings-content {
        -webkit-overflow-scrolling: touch;
        transform: translateZ(0); /* Force hardware acceleration */
    }

    /* Prevent horizontal overflow */
    .app-container,
    .main-content,
    .chat-container,
    .input-container {
        max-width: 100vw;
        overflow-x: hidden;
    }

    /* Better button spacing for touch */
    .header-actions button {
        margin: 0 2px;
        min-width: 40px;
        min-height: 40px;
    }

    /* Improve sidebar touch interactions */
    .sidebar {
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%);
        will-change: transform;
    }

    .sidebar.open {
        -webkit-transform: translateX(0);
        transform: translateX(0);
    }

    /* Add backdrop for sidebar */
    .sidebar-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        backdrop-filter: blur(2px);
        -webkit-backdrop-filter: blur(2px);
        /* Improve performance */
        will-change: opacity;
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
    }

    .sidebar-backdrop.show {
        opacity: 1;
        visibility: visible;
    }

    /* Improve code block mobile experience */
    .message-content pre {
        max-width: calc(100vw - 32px);
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Better table handling on mobile */
    .message-content table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;
    }

    /* Improve form elements */
    .auth-form input,
    #messageInput {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        border-radius: 8px;
    }

    /* Fix viewport issues */
    .app-container {
        min-height: 100vh;
        min-height: -webkit-fill-available;
    }

    /* Specific fixes for Samsung Browser */
    .input-container {
        /* Ensure input area is always visible */
        position: relative;
        bottom: 0;
        /* Use safe area insets for devices with notches */
        padding-bottom: calc(12px + env(safe-area-inset-bottom));
    }

    /* Ensure header stays at top */
    .header {
        position: sticky;
        top: 0;
        z-index: 100;
        /* Use safe area insets for devices with notches */
        padding-top: calc(8px + env(safe-area-inset-top));
    }

    /* Better handling of keyboard on mobile */
    .input-wrapper:focus-within {
        /* Prevent layout shift when keyboard appears */
        transform: none;
    }

    /* Improve scrolling behavior */
    .chat-container {
        /* Ensure smooth scrolling */
        scroll-behavior: smooth;
        /* Better momentum scrolling on iOS */
        -webkit-overflow-scrolling: touch;
        /* Prevent rubber band effect */
        overscroll-behavior: contain;
    }

    /* Improve swipe gesture performance */
    .main-content {
        /* Improve touch responsiveness */
        touch-action: pan-y;
        /* Optimize for animations */
        will-change: transform;
    }

    /* Re-enable text selection for message content */
    .message-content {
        -webkit-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        user-select: text;
    }

    /* Improve sidebar swipe area indicator */
    .main-content::before {
        content: '';
        position: fixed;
        left: 0;
        top: 0;
        width: 25px;
        height: 100%;
        z-index: 10;
        pointer-events: none;
        /* Subtle visual indicator for swipe area */
        background: linear-gradient(to right, rgba(255, 255, 255, 0.01), transparent);
        opacity: 0.5;
    }
}

@media (max-width: 480px) {
    .notification-toast {
        top: 8px;
        right: 8px;
        left: 8px;
        padding: 12px;
        font-size: 13px;
    }
}

/* Custom Confirmation Dialog */
.custom-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(8px);
    z-index: 10001;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.custom-dialog-overlay.show {
    opacity: 1;
    visibility: visible;
}

.custom-dialog-overlay.fade-out {
    opacity: 0;
    transform: scale(0.95);
}

.custom-dialog {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 1px solid #4d4d4d;
    border-radius: 16px;
    width: 90%;
    max-width: 480px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.custom-dialog-overlay.show .custom-dialog {
    transform: scale(1) translateY(0);
}

.dialog-header {
    padding: 24px 24px 16px 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dialog-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.dialog-icon.default {
    background: rgba(0, 255, 255, 0.2);
    color: #00FFFF;
    border: 2px solid rgba(0, 255, 255, 0.3);
}

.dialog-icon.danger {
    background: rgba(255, 107, 107, 0.2);
    color: #ff6b6b;
    border: 2px solid rgba(255, 107, 107, 0.3);
    animation: pulse-danger 2s infinite ease-in-out;
}

@keyframes pulse-danger {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.4);
    }
    50% {
        box-shadow: 0 0 0 8px rgba(255, 107, 107, 0);
    }
}

.dialog-title {
    color: #ffffff;
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    flex: 1;
}

.dialog-content {
    padding: 16px 24px 24px 24px;
}

.dialog-message {
    color: #ffffff;
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 8px 0;
    line-height: 1.5;
}

.dialog-description {
    color: #b3b3b3;
    font-size: 14px;
    margin: 0;
    line-height: 1.5;
}

.dialog-actions {
    padding: 0 24px 24px 24px;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.dialog-btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    min-width: 100px;
    position: relative;
    overflow: hidden;
}

.dialog-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.dialog-btn:hover::before {
    left: 100%;
}

.dialog-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dialog-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.dialog-btn.danger {
    background: linear-gradient(135deg, #ff6b6b, #ff5252);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.dialog-btn.danger:hover {
    background: linear-gradient(135deg, #ff5252, #ff4444);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
    transform: translateY(-2px);
}

.dialog-btn.default {
    background: linear-gradient(135deg, #00FFFF, #00CCCC);
    color: #000000;
    box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
}

.dialog-btn.default:hover {
    background: linear-gradient(135deg, #00CCCC, #00AAAA);
    box-shadow: 0 6px 20px rgba(0, 255, 255, 0.4);
    transform: translateY(-2px);
}

/* Responsive Dialog Styles */
@media (max-width: 768px) {
    .custom-dialog {
        width: 95%;
        max-width: none;
        margin: 10px;
    }

    .dialog-header {
        padding: 20px 20px 12px 20px;
    }

    .dialog-content {
        padding: 12px 20px 20px 20px;
    }

    .dialog-actions {
        padding: 0 20px 20px 20px;
        flex-direction: column;
        gap: 8px;
    }

    .dialog-btn {
        width: 100%;
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .dialog-header {
        padding: 16px 16px 8px 16px;
    }

    .dialog-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .dialog-title {
        font-size: 18px;
    }

    .dialog-content {
        padding: 8px 16px 16px 16px;
    }

    .dialog-message {
        font-size: 15px;
    }

    .dialog-description {
        font-size: 13px;
    }

    .dialog-actions {
        padding: 0 16px 16px 16px;
    }

    .dialog-btn {
        padding: 14px 20px;
        font-size: 15px;
    }
}

/* Input Dialog Specific Styles */
.input-dialog {
    max-width: 520px;
}

.input-group {
    margin-top: 20px;
}

.input-label {
    display: block;
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.dialog-input {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 16px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    box-sizing: border-box;
}

.dialog-input:focus {
    border-color: rgba(0, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.1);
}

.dialog-input.valid {
    border-color: rgba(0, 255, 0, 0.6);
    background: rgba(0, 255, 0, 0.1);
}

.dialog-input.invalid {
    border-color: rgba(255, 107, 107, 0.6);
    background: rgba(255, 107, 107, 0.1);
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.input-hint {
    margin-top: 8px;
    font-size: 12px;
    color: #8d8d8d;
    display: flex;
    align-items: center;
    gap: 4px;
}

.expected-text {
    background: rgba(0, 255, 255, 0.2);
    color: #00FFFF;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

.dialog-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.dialog-btn.enabled {
    opacity: 1;
    cursor: pointer;
}

.dialog-btn.danger.enabled {
    animation: pulse-ready 2s infinite ease-in-out;
}

@keyframes pulse-ready {
    0%, 100% {
        box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    }
    50% {
        box-shadow: 0 6px 25px rgba(255, 107, 107, 0.5);
    }
}

/* Input Dialog Responsive Styles */
@media (max-width: 768px) {
    .input-dialog {
        width: 95%;
        max-width: none;
    }

    .dialog-input {
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

@media (max-width: 480px) {
    .input-group {
        margin-top: 16px;
    }

    .dialog-input {
        padding: 14px 16px;
        font-size: 16px;
    }

    .input-hint {
        font-size: 11px;
    }
}
